# HTTPS Setup for Laravel Docker Development

This document describes the HTTPS configuration for the Laravel application running in Docker.

## Quick Start

Run the setup script to automatically configure HTTPS:

```bash
./setup-https.sh
```

## Manual Setup

### 1. Generate SSL Certificates

```bash
mkdir -p docker/nginx/ssl
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout docker/nginx/ssl/nginx.key \
    -out docker/nginx/ssl/nginx.crt \
    -subj "/C=US/ST=Local/L=Local/O=Development/OU=IT Department/CN=localhost"
```

### 2. Start Docker Services

```bash
docker-compose up -d --build
```

### 3. Install Dependencies

```bash
docker-compose exec app composer install
docker-compose exec app php artisan key:generate
docker-compose exec app php artisan migrate
```

## Access URLs

- **HTTP**: http://localhost:8000
- **HTTPS**: https://localhost:8443

## SSL Certificate Warning

When accessing the HTTPS URL for the first time, your browser will show a security warning because we're using a self-signed certificate. This is normal for local development.

**To proceed:**
1. Click "Advanced" or "Show Details"
2. Click "Proceed to localhost" or "Accept Risk and Continue"

## Environment Configuration

### Required Environment Variables

Update your `.env` file with these HTTPS-related variables:

```env
APP_URL=http://localhost:8000
APP_URL_HTTPS=https://localhost:8443
SANCTUM_STATEFUL_DOMAINS=localhost:8000,localhost:8443,127.0.0.1:8000,127.0.0.1:8443
```

### Laravel Sanctum Configuration

The application is configured to work with both HTTP and HTTPS for API authentication. The following domains are automatically included in Sanctum's stateful domains:

- localhost:8000 (HTTP)
- localhost:8443 (HTTPS)
- 127.0.0.1:8000 (HTTP)
- 127.0.0.1:8443 (HTTPS)

## Docker Configuration

### Ports

- **80 → 8000**: HTTP traffic
- **443 → 8443**: HTTPS traffic

### SSL Certificates

SSL certificates are mounted as read-only volumes:
- Host: `./docker/nginx/ssl`
- Container: `/etc/nginx/ssl:ro`

## Nginx Configuration

The Nginx configuration includes:

- **SSL Protocols**: TLSv1.2, TLSv1.3
- **Security Headers**: HSTS, X-Frame-Options, X-Content-Type-Options
- **SSL Session Caching**: 10m cache with 10m timeout
- **HTTP/2 Support**: Enabled for HTTPS

## IntelliJ IDEA Integration

The project includes pre-configured run configurations:

### Docker Compose
- **Docker Compose Up**: Start all services
- **Docker Compose Down**: Stop all services

### Laravel Commands
- **Laravel Migrate**: Run database migrations
- **Laravel Seed**: Run database seeders
- **Laravel Tinker**: Open Laravel Tinker shell
- **Laravel Queue Work**: Start queue worker

### Browser Launch
- **Open HTTP (localhost:8000)**: Launch HTTP version
- **Open HTTPS (localhost:8443)**: Launch HTTPS version

### Debugging
- **PHP Debug Docker**: Remote PHP debugging configuration

## Troubleshooting

### SSL Certificate Issues

If you encounter SSL certificate issues:

1. Regenerate certificates:
   ```bash
   rm -rf docker/nginx/ssl
   ./setup-https.sh
   ```

2. Clear browser cache and cookies for localhost

### Container Issues

If containers fail to start:

1. Check Docker logs:
   ```bash
   docker-compose logs nginx
   docker-compose logs app
   ```

2. Rebuild containers:
   ```bash
   docker-compose down
   docker-compose up -d --build
   ```

### Permission Issues

If you encounter permission issues with SSL certificates:

```bash
sudo chown -R $USER:$USER docker/nginx/ssl
chmod 644 docker/nginx/ssl/nginx.crt
chmod 600 docker/nginx/ssl/nginx.key
```

## Security Notes

- SSL certificates are self-signed and only suitable for local development
- The `docker/nginx/ssl` directory is added to `.gitignore` to prevent committing certificates
- For production, use proper SSL certificates from a trusted CA

## File Structure

```
docker/
├── nginx/
│   ├── conf.d/
│   │   └── app.conf          # Nginx configuration with HTTPS
│   └── ssl/                  # SSL certificates (gitignored)
│       ├── nginx.crt         # SSL certificate
│       └── nginx.key         # SSL private key
├── docker-compose.yml        # Updated with HTTPS ports and volumes
├── .env.example             # Updated with HTTPS variables
└── setup-https.sh           # Automated setup script
```

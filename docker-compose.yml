services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: laravel_app
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
    networks:
      - laravel_net
    depends_on:
      - db
      - redis

  nginx:
    image: nginx:alpine
    container_name: laravel_nginx
    restart: unless-stopped
    ports:
      - "8000:80"
      - "8443:443"
    volumes:
      - ./:/var/www
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
      - ./docker/nginx/ssl:/etc/nginx/ssl:ro
    networks:
      - laravel_net
    depends_on:
      - app

  db:
    image: postgres:13
    container_name: laravel_db
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DB_DATABASE:-laravel}
      POSTGRES_USER: ${DB_USERNAME:-forge}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-secret}
    volumes:
      - pgdata:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - laravel_net

  redis:
    image: redis:alpine
    container_name: laravel_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - laravel_net

  node:
    image: node:16
    container_name: laravel_node
    volumes:
      - ./:/var/www
    working_dir: /var/www
    networks:
      - laravel_net
    command: ["tail", "-f", "/dev/null"]

networks:
  laravel_net:
    driver: bridge

volumes:
  pgdata:
    driver: local

<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        if (config('app.env') === 'production') {
            \Illuminate\Support\Facades\URL::forceScheme('https');
        }

        // Force HTTPS scheme when accessing via HTTPS port in local development
        if (config('app.env') === 'local' && request()->server('SERVER_PORT') == 443) {
            \Illuminate\Support\Facades\URL::forceScheme('https');
        }
    }
}
